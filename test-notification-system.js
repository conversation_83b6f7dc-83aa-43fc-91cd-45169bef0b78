// Test script for the reminder notification system
// Run this in the browser console to test notifications

console.log('🔔 Reminder Notification System Test Script');
console.log('==========================================');

// Test 1: Check notification support
function testNotificationSupport() {
  console.log('\n📋 Test 1: Notification Support');
  
  if ('Notification' in window) {
    console.log('✅ Browser supports notifications');
    console.log(`📊 Current permission: ${Notification.permission}`);
    
    if (Notification.permission === 'granted') {
      console.log('✅ Notification permission granted');
    } else if (Notification.permission === 'denied') {
      console.log('❌ Notification permission denied');
    } else {
      console.log('⚠️ Notification permission not requested yet');
    }
  } else {
    console.log('❌ Browser does not support notifications');
  }
}

// Test 2: Request notification permission
async function testNotificationPermission() {
  console.log('\n📋 Test 2: Request Notification Permission');
  
  if ('Notification' in window) {
    try {
      const permission = await Notification.requestPermission();
      console.log(`📊 Permission result: ${permission}`);
      
      if (permission === 'granted') {
        console.log('✅ Permission granted successfully');
        return true;
      } else {
        console.log('❌ Permission denied or dismissed');
        return false;
      }
    } catch (error) {
      console.error('❌ Error requesting permission:', error);
      return false;
    }
  } else {
    console.log('❌ Notifications not supported');
    return false;
  }
}

// Test 3: Send test notification
function testNotification() {
  console.log('\n📋 Test 3: Send Test Notification');
  
  if (Notification.permission === 'granted') {
    const notification = new Notification('Test Reminder', {
      body: 'This is a test reminder notification from Ukshati dashboard',
      icon: '/favicon.ico',
      tag: 'test-reminder',
      requireInteraction: true
    });
    
    notification.onclick = () => {
      console.log('🖱️ Test notification clicked');
      notification.close();
    };
    
    notification.onshow = () => {
      console.log('✅ Test notification displayed');
    };
    
    notification.onclose = () => {
      console.log('📴 Test notification closed');
    };
    
    console.log('📤 Test notification sent');
  } else {
    console.log('❌ Cannot send notification - permission not granted');
  }
}

// Test 4: Test Web Audio API
function testNotificationSound() {
  console.log('\n📋 Test 4: Test Notification Sound');
  
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = 800;
    oscillator.type = 'sine';
    
    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
    
    console.log('🔊 Test notification sound played');
  } catch (error) {
    console.error('❌ Error playing notification sound:', error);
  }
}

// Test 5: Test API endpoints
async function testReminderAPI() {
  console.log('\n📋 Test 5: Test Reminder API');
  
  try {
    // Test getting all reminders
    console.log('📡 Testing GET /api/reminders...');
    const allReminders = await fetch('/api/reminders');
    const allRemindersData = await allReminders.json();
    console.log(`📊 Total reminders: ${allRemindersData.length}`);
    
    // Test checking due reminders
    console.log('📡 Testing GET /api/reminders?check=true...');
    const dueReminders = await fetch('/api/reminders?check=true');
    const dueRemindersData = await dueReminders.json();
    console.log(`📊 Due reminders: ${dueRemindersData.length}`);
    
    if (dueRemindersData.length > 0) {
      console.log('📋 Due reminders found:');
      dueRemindersData.forEach((reminder, index) => {
        console.log(`  ${index + 1}. ${reminder.cname}: ${reminder.message} (${reminder.datetime})`);
      });
    } else {
      console.log('📭 No due reminders found');
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

// Test 6: Create a test reminder (due in 1 minute)
async function createTestReminder() {
  console.log('\n📋 Test 6: Create Test Reminder');
  
  try {
    // Get current time + 1 minute
    const now = new Date();
    const testTime = new Date(now.getTime() + 60000); // 1 minute from now
    
    const testReminder = {
      message: 'Test reminder created by notification test script',
      reminder_date: testTime.toISOString().split('T')[0],
      reminder_time: testTime.toTimeString().split(' ')[0].substring(0, 5),
      cid: 1 // Assuming customer ID 1 exists
    };
    
    console.log('📤 Creating test reminder:', testReminder);
    
    const response = await fetch('/api/reminders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testReminder),
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Test reminder created successfully:', result);
      console.log('⏰ Reminder will be due in 1 minute');
    } else {
      const error = await response.json();
      console.error('❌ Failed to create test reminder:', error);
    }
    
  } catch (error) {
    console.error('❌ Error creating test reminder:', error);
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running all notification system tests...\n');
  
  testNotificationSupport();
  
  const hasPermission = await testNotificationPermission();
  
  if (hasPermission) {
    testNotification();
  }
  
  testNotificationSound();
  await testReminderAPI();
  
  console.log('\n🎯 Test Summary:');
  console.log('- Check browser console for detailed results');
  console.log('- If you created a test reminder, check dashboard in 1 minute');
  console.log('- Make sure to test on the actual dashboard page');
  
  console.log('\n📝 Manual Tests to Perform:');
  console.log('1. Navigate to dashboard and check for notification banner');
  console.log('2. Create a reminder due in 1-2 minutes via CRM → Reminders');
  console.log('3. Wait and observe dashboard for notifications');
  console.log('4. Test dismiss functionality');
  console.log('5. Test "View Customer" navigation');
}

// Expose functions to global scope for manual testing
window.testNotificationSystem = {
  runAllTests,
  testNotificationSupport,
  testNotificationPermission,
  testNotification,
  testNotificationSound,
  testReminderAPI,
  createTestReminder
};

console.log('\n🛠️ Available test functions:');
console.log('- testNotificationSystem.runAllTests() - Run all tests');
console.log('- testNotificationSystem.testNotification() - Send test notification');
console.log('- testNotificationSystem.testNotificationSound() - Play test sound');
console.log('- testNotificationSystem.createTestReminder() - Create test reminder');
console.log('\nExample: testNotificationSystem.runAllTests()');
