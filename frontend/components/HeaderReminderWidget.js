"use client";
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaBell, FaEye, FaTimes, FaClock } from 'react-icons/fa';

const HeaderReminderWidget = () => {
  const [reminders, setReminders] = useState([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check for active reminders (both due and upcoming)
  const checkActiveReminders = async () => {
    try {
      // Get all reminders
      const response = await fetch('/api/reminders');
      if (!response.ok) throw new Error('Failed to fetch reminders');
      
      const allReminders = await response.json();
      
      // Filter for active reminders (due now or within next 24 hours)
      const now = new Date();
      const next24Hours = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      
      const activeReminders = allReminders.filter(reminder => {
        const reminderTime = new Date(reminder.datetime);
        return reminderTime <= next24Hours;
      }).map(reminder => {
        const reminderTime = new Date(reminder.datetime);
        const isDue = reminderTime <= now;
        const timeUntil = reminderTime.getTime() - now.getTime();
        const hoursUntil = Math.floor(timeUntil / (1000 * 60 * 60));
        const minutesUntil = Math.floor((timeUntil % (1000 * 60 * 60)) / (1000 * 60));
        
        return {
          ...reminder,
          isDue,
          timeUntil: isDue ? 'Due now' : 
                    hoursUntil > 0 ? `${hoursUntil}h ${minutesUntil}m` : 
                    minutesUntil > 0 ? `${minutesUntil}m` : 'Due now'
        };
      }).sort((a, b) => {
        // Sort by due status first, then by time
        if (a.isDue && !b.isDue) return -1;
        if (!a.isDue && b.isDue) return 1;
        return new Date(a.datetime) - new Date(b.datetime);
      });

      setReminders(activeReminders);
    } catch (error) {
      console.error('Error checking active reminders:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial check and periodic updates
  useEffect(() => {
    checkActiveReminders();
    
    // Check every 30 seconds for updates
    const interval = setInterval(checkActiveReminders, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Dismiss a reminder
  const dismissReminder = async (reminderId) => {
    try {
      await fetch('/api/reminders?markNotified=true', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reminderIds: [reminderId] }),
      });
      
      // Remove from local state
      setReminders(prev => prev.filter(r => r.rid !== reminderId));
    } catch (error) {
      console.error('Error dismissing reminder:', error);
    }
  };

  // Count due reminders
  const dueCount = reminders.filter(r => r.isDue).length;
  const totalCount = reminders.length;

  // Don't render if no active reminders
  if (totalCount === 0) {
    return null;
  }

  return (
    <div className="relative">
      {/* Bell Icon with Badge */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="relative p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-all"
        title={`${dueCount} due, ${totalCount} total reminders`}
      >
        <motion.div
          animate={dueCount > 0 ? { scale: [1, 1.1, 1] } : {}}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <FaBell className={`text-lg ${dueCount > 0 ? 'text-orange-400' : 'text-gray-300'}`} />
        </motion.div>
        
        {/* Badge */}
        {totalCount > 0 && (
          <span className={`absolute -top-1 -right-1 min-w-[18px] h-[18px] text-xs font-bold rounded-full flex items-center justify-center ${
            dueCount > 0 ? 'bg-red-500 text-white' : 'bg-blue-500 text-white'
          }`}>
            {totalCount > 9 ? '9+' : totalCount}
          </span>
        )}
      </button>

      {/* Dropdown Panel */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-80 bg-black rounded-lg shadow-xl border border-gray-700 z-50 max-h-96 overflow-hidden"
          >
            {/* Header */}
            <div className="p-3 border-b border-gray-700 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FaBell className="text-orange-400" />
                <span className="font-medium text-white">Active Reminders</span>
                <span className="text-xs text-gray-400">({totalCount})</span>
              </div>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-1 text-gray-400 hover:text-white transition-colors"
              >
                <FaTimes className="text-sm" />
              </button>
            </div>

            {/* Reminders List */}
            <div className="max-h-80 overflow-y-auto">
              {reminders.map((reminder) => (
                <motion.div
                  key={reminder.rid}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className={`p-3 border-b border-gray-700/50 hover:bg-gray-700/30 transition-colors ${
                    reminder.isDue ? 'bg-red-500/10' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {/* Customer Name */}
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-white text-sm truncate">
                          {reminder.cname}
                        </span>
                        <span className={`px-2 py-0.5 text-xs rounded-full ${
                          reminder.isDue 
                            ? 'bg-red-500/20 text-red-300' 
                            : 'bg-blue-500/20 text-blue-300'
                        }`}>
                          {reminder.timeUntil}
                        </span>
                      </div>
                      
                      {/* Message */}
                      <p className="text-gray-300 text-sm mb-2 line-clamp-2">
                        {reminder.message}
                      </p>
                      
                      {/* Time Info */}
                      <div className="flex items-center gap-3 text-xs text-gray-400">
                        <div className="flex items-center gap-1">
                          <FaClock />
                          <span>
                            {new Date(reminder.datetime).toLocaleString('en-IN', {
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: true
                            })}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center gap-1 ml-2">
                      <button
                        onClick={() => window.location.href = '/crm/customers'}
                        className="p-1.5 text-blue-400 hover:text-blue-300 transition-colors"
                        title="View Customer"
                      >
                        <FaEye className="text-xs" />
                      </button>
                      <button
                        onClick={() => dismissReminder(reminder.rid)}
                        className="p-1.5 text-gray-400 hover:text-white transition-colors"
                        title="Dismiss"
                      >
                        <FaTimes className="text-xs" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Footer */}
            <div className="p-3 border-t border-gray-700 bg-gray-800/50">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => window.location.href = '/crm/reminders'}
                  className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                >
                  View All Reminders →
                </button>
                <button
                  onClick={checkActiveReminders}
                  className="px-2 py-1 bg-gray-600/50 hover:bg-gray-600/70 text-gray-300 text-xs rounded transition-colors"
                >
                  Refresh
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default HeaderReminderWidget;
