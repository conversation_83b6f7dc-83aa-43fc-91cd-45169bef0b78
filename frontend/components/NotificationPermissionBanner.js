"use client";
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaBell, FaTimes } from 'react-icons/fa';

const NotificationPermissionBanner = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [permission, setPermission] = useState('default');

  useEffect(() => {
    // Check notification permission status
    if (typeof window !== 'undefined' && 'Notification' in window) {
      const currentPermission = Notification.permission;
      setPermission(currentPermission);
      
      // Show banner if permission is not granted
      if (currentPermission === 'default') {
        setShowBanner(true);
      }
    }
  }, []);

  const requestPermission = async () => {
    if ('Notification' in window) {
      const result = await Notification.requestPermission();
      setPermission(result);
      
      if (result === 'granted') {
        setShowBanner(false);
        // Show a test notification
        new Notification('Notifications Enabled!', {
          body: 'You will now receive reminder alerts on this dashboard.',
          icon: '/favicon.ico'
        });
      }
    }
  };

  const dismissBanner = () => {
    setShowBanner(false);
    // Store dismissal in localStorage to not show again for this session
    localStorage.setItem('notificationBannerDismissed', 'true');
  };

  // Don't show if permission is already granted or denied, or if dismissed
  if (permission !== 'default' || !showBanner) {
    return null;
  }

  // Check if banner was dismissed in this session
  if (typeof window !== 'undefined' && localStorage.getItem('notificationBannerDismissed')) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="mb-4"
      >
        <div className="bg-gradient-to-r from-blue-600/20 to-indigo-600/20 border border-blue-500/30 rounded-lg p-4 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <FaBell className="text-blue-400" />
              </div>
              <div>
                <h3 className="text-white font-medium">Enable Reminder Notifications</h3>
                <p className="text-sm text-gray-300">
                  Get instant alerts when your reminders are due, even when you're not on this page.
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={requestPermission}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
              >
                Enable Notifications
              </button>
              <button
                onClick={dismissBanner}
                className="p-2 text-gray-400 hover:text-white transition-colors"
              >
                <FaTimes />
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default NotificationPermissionBanner;
