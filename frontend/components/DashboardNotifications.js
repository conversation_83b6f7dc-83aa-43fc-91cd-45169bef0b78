"use client";
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaBell, FaTimes, FaCalendarAlt, FaUser, FaClock } from 'react-icons/fa';

const DashboardNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [isVisible, setIsVisible] = useState(true);
  const [loading, setLoading] = useState(true);
  const [lastNotificationCount, setLastNotificationCount] = useState(0);

  // Check for due reminders
  const checkDueReminders = async () => {
    try {
      const response = await fetch('/api/reminders?check=true');
      if (!response.ok) throw new Error('Failed to fetch reminders');

      const dueReminders = await response.json();

      // Format notifications
      const formattedNotifications = dueReminders.map(reminder => ({
        id: reminder.rid,
        type: 'reminder',
        title: `Reminder for ${reminder.cname}`,
        message: reminder.message,
        customerName: reminder.cname,
        customerId: reminder.cid,
        datetime: reminder.datetime,
        reminderDate: reminder.reminder_date,
        reminderTime: reminder.reminder_time,
        timestamp: new Date(reminder.datetime)
      }));

      setNotifications(formattedNotifications);

      // Show browser notifications for new reminders
      if (formattedNotifications.length > lastNotificationCount) {
        const newNotifications = formattedNotifications.slice(lastNotificationCount);
        showBrowserNotifications(newNotifications);
      }

      setLastNotificationCount(formattedNotifications.length);
    } catch (error) {
      console.error('Error checking due reminders:', error);
    } finally {
      setLoading(false);
    }
  };

  // Show browser notifications
  const showBrowserNotifications = (newNotifications) => {
    // Request permission if not granted
    if (Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          sendNotifications(newNotifications);
        }
      });
    } else if (Notification.permission === 'granted') {
      sendNotifications(newNotifications);
    }
  };

  // Send individual notifications
  const sendNotifications = (notificationsToSend) => {
    notificationsToSend.forEach(notification => {
      const browserNotification = new Notification(`Reminder: ${notification.customerName}`, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: `reminder-${notification.id}`,
        requireInteraction: true
      });

      browserNotification.onclick = () => {
        window.focus();
        browserNotification.close();
        // Navigate to customer page
        window.location.href = `/crm/customers`;
      };

      // Play notification sound
      playNotificationSound();
    });
  };

  // Play notification sound
  const playNotificationSound = () => {
    try {
      // Create a simple beep sound using Web Audio API
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 800;
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.5);
    } catch (error) {
      console.log('Audio playback not available:', error);
    }
  };

  // Initial check and periodic updates
  useEffect(() => {
    checkDueReminders();

    // Check every minute for new due reminders
    const interval = setInterval(checkDueReminders, 60000);

    return () => clearInterval(interval);
  }, []);

  // Dismiss a notification and mark as notified
  const dismissNotification = async (notificationId) => {
    try {
      // Mark as notified in the backend
      await fetch('/api/reminders?markNotified=true', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reminderIds: [notificationId] }),
      });

      // Remove from local state
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Error dismissing notification:', error);
      // Still remove from UI even if backend call fails
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
    }
  };

  // Dismiss all notifications and mark as notified
  const dismissAll = async () => {
    try {
      const reminderIds = notifications.map(n => n.id);

      if (reminderIds.length > 0) {
        // Mark all as notified in the backend
        await fetch('/api/reminders?markNotified=true', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ reminderIds }),
        });
      }

      // Clear local state
      setNotifications([]);
    } catch (error) {
      console.error('Error dismissing all notifications:', error);
      // Still clear UI even if backend call fails
      setNotifications([]);
    }
  };

  // Hide the entire notification panel
  const hidePanel = () => {
    setIsVisible(false);
  };

  // Format time for display
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Don't render if no notifications or panel is hidden
  if (!isVisible || notifications.length === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="mb-6"
      >
        <div className="bg-gradient-to-r from-orange-600/20 to-red-600/20 border border-orange-500/30 rounded-xl p-4 backdrop-blur-sm">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-500/20 rounded-lg">
                <FaBell className="text-orange-400 animate-pulse" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">
                  Due Reminders ({notifications.length})
                </h3>
                <p className="text-sm text-gray-300">
                  You have {notifications.length} reminder{notifications.length !== 1 ? 's' : ''} that need attention
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {notifications.length > 1 && (
                <button
                  onClick={dismissAll}
                  className="px-3 py-1 text-xs bg-gray-600/50 hover:bg-gray-600/70 text-gray-300 rounded-lg transition-colors"
                >
                  Dismiss All
                </button>
              )}
              <button
                onClick={hidePanel}
                className="p-2 text-gray-400 hover:text-white transition-colors"
              >
                <FaTimes />
              </button>
            </div>
          </div>

          {/* Notifications List */}
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {notifications.map((notification) => (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="bg-black/30 rounded-lg p-4 border border-gray-700/50"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <FaUser className="text-blue-400 text-sm" />
                      <span className="font-medium text-white">
                        {notification.customerName}
                      </span>
                      <span className="px-2 py-1 bg-orange-500/20 text-orange-300 text-xs rounded-full">
                        Due Now
                      </span>
                    </div>

                    <p className="text-gray-200 mb-3">
                      {notification.message}
                    </p>

                    <div className="flex items-center gap-4 text-sm text-gray-400">
                      <div className="flex items-center gap-1">
                        <FaCalendarAlt className="text-xs" />
                        <span>{formatTime(notification.datetime)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FaClock className="text-xs" />
                        <span>Due now</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => window.location.href = `/crm/customers`}
                      className="px-3 py-1 bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 text-xs rounded-lg transition-colors"
                    >
                      View Customer
                    </button>
                    <button
                      onClick={() => dismissNotification(notification.id)}
                      className="p-1 text-gray-400 hover:text-white transition-colors"
                    >
                      <FaTimes className="text-xs" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="mt-4 pt-3 border-t border-gray-700/50">
            <div className="flex items-center justify-between">
              <button
                onClick={() => window.location.href = '/crm/reminders'}
                className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
              >
                View All Reminders →
              </button>
              <button
                onClick={checkDueReminders}
                className="px-3 py-1 bg-gray-600/50 hover:bg-gray-600/70 text-gray-300 text-xs rounded-lg transition-colors"
              >
                Refresh
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default DashboardNotifications;
